/**
 * Represents a chat message in the system
 */
interface ChatMessage {
    id: string;
    content: string;
    role: 'user' | 'assistant' | 'system';
    timestamp: string;
    metadata?: Record<string, any>;
}
/**
 * Represents a chat session
 */
interface ChatSession {
    id: string;
    userId: string;
    messages: ChatMessage[];
    createdAt: string;
    updatedAt: string;
    metadata?: Record<string, any>;
}
/**
 * Represents a chat completion request to the LLM
 */
interface ChatCompletionRequest {
    messages: ChatMessage[];
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
    tools?: Tool[];
}
/**
 * Represents a tool that can be called by the LLM
 */
interface Tool {
    type: string;
    name: string;
    description: string;
    parameters: Record<string, any>;
}
/**
 * Represents a tool call made by the LLM
 */
interface ToolCall {
    toolId: string;
    toolName: string;
    arguments: Record<string, any>;
}
/**
 * Represents the result of a tool call
 */
interface ToolCallResult {
    toolCallId: string;
    result: any;
    error?: string;
}

/**
 * Represents a user in the system
 */
interface User {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    metadata?: Record<string, any>;
}
/**
 * Represents authentication credentials
 */
interface AuthCredentials {
    username: string;
    password: string;
}
/**
 * Represents a JWT token response
 */
interface TokenResponse {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
}
/**
 * Represents a refresh token request
 */
interface RefreshTokenRequest {
    refreshToken: string;
}
/**
 * Represents the payload of a JWT token
 */
interface JwtPayload {
    sub: string;
    username: string;
    roles: string[];
    permissions: string[];
    iat: number;
    exp: number;
}

/**
 * Represents an OTRS ticket
 */
interface OtrsTicket {
    id: string;
    title: string;
    description: string;
    status: TicketStatus;
    priority: TicketPriority;
    queue: string;
    customer: string;
    agent?: string;
    createdAt: string;
    updatedAt: string;
    tags: string[];
    attachments: Attachment[];
    customFields?: Record<string, any>;
}
/**
 * Represents the status of a ticket
 */
declare enum TicketStatus {
    NEW = "new",
    OPEN = "open",
    PENDING = "pending",
    RESOLVED = "resolved",
    CLOSED = "closed"
}
/**
 * Represents the priority of a ticket
 */
declare enum TicketPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
/**
 * Represents an attachment in a ticket
 */
interface Attachment {
    id: string;
    filename: string;
    contentType: string;
    size: number;
    url: string;
}
/**
 * Represents a request to create a new ticket
 */
interface CreateTicketRequest {
    title: string;
    description: string;
    priority: TicketPriority;
    queue: string;
    customer: string;
    tags?: string[];
    attachments?: Attachment[];
    customFields?: Record<string, any>;
}
/**
 * Represents a request to update an existing ticket
 */
interface UpdateTicketRequest {
    id: string;
    title?: string;
    description?: string;
    status?: TicketStatus;
    priority?: TicketPriority;
    queue?: string;
    agent?: string;
    tags?: string[];
    customFields?: Record<string, any>;
}

/**
 * API endpoints for the chat service
 */
declare const CHAT_API: {
    SEND_MESSAGE: string;
    GET_HISTORY: string;
    GET_SESSION: string;
    CREATE_SESSION: string;
    DELETE_SESSION: string;
};
/**
 * API endpoints for authentication
 */
declare const AUTH_API: {
    LOGIN: string;
    LOGOUT: string;
    REFRESH_TOKEN: string;
    REGISTER: string;
    ME: string;
};
/**
 * API endpoints for OTRS integration
 */
declare const OTRS_API: {
    CREATE_TICKET: string;
    UPDATE_TICKET: string;
    GET_TICKET: string;
    SEARCH_TICKETS: string;
    GET_QUEUES: string;
    GET_AGENTS: string;
};
/**
 * WebSocket events
 */
declare const WEBSOCKET_EVENTS: {
    CONNECT: string;
    DISCONNECT: string;
    MESSAGE: string;
    TYPING: string;
    ERROR: string;
};

/**
 * Error codes for the application
 */
declare enum ErrorCode {
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS",
    TOKEN_EXPIRED = "TOKEN_EXPIRED",
    UNAUTHORIZED = "UNAUTHORIZED",
    FORBIDDEN = "FORBIDDEN",
    MESSAGE_FAILED = "MESSAGE_FAILED",
    SESSION_NOT_FOUND = "SESSION_NOT_FOUND",
    TICKET_CREATION_FAILED = "TICKET_CREATION_FAILED",
    TICKET_UPDATE_FAILED = "TICKET_UPDATE_FAILED",
    TICKET_NOT_FOUND = "TICKET_NOT_FOUND",
    LLM_REQUEST_FAILED = "LLM_REQUEST_FAILED",
    TOOL_EXECUTION_FAILED = "TOOL_EXECUTION_FAILED",
    EMBEDDING_FAILED = "EMBEDDING_FAILED",
    RETRIEVAL_FAILED = "RETRIEVAL_FAILED",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
    NOT_FOUND = "NOT_FOUND",
    BAD_REQUEST = "BAD_REQUEST"
}
/**
 * Error messages for the application
 */
declare const ErrorMessage: {
    INVALID_CREDENTIALS: string;
    TOKEN_EXPIRED: string;
    UNAUTHORIZED: string;
    FORBIDDEN: string;
    MESSAGE_FAILED: string;
    SESSION_NOT_FOUND: string;
    TICKET_CREATION_FAILED: string;
    TICKET_UPDATE_FAILED: string;
    TICKET_NOT_FOUND: string;
    LLM_REQUEST_FAILED: string;
    TOOL_EXECUTION_FAILED: string;
    EMBEDDING_FAILED: string;
    RETRIEVAL_FAILED: string;
    VALIDATION_ERROR: string;
    INTERNAL_SERVER_ERROR: string;
    NOT_FOUND: string;
    BAD_REQUEST: string;
};

/**
 * Formats a date string to a human-readable format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
declare function formatDate(dateString: string): string;
/**
 * Truncates a string to a specified length
 * @param str String to truncate
 * @param maxLength Maximum length
 * @returns Truncated string
 */
declare function truncateString(str: string, maxLength: number): string;
/**
 * Formats a message for display
 * @param message Message content
 * @returns Formatted message
 */
declare function formatMessage(message: string): string;
/**
 * Formats a file size in bytes to a human-readable format
 * @param bytes File size in bytes
 * @returns Formatted file size
 */
declare function formatFileSize(bytes: number): string;

/**
 * Validates an email address
 * @param email Email address to validate
 * @returns Whether the email is valid
 */
declare function isValidEmail(email: string): boolean;
/**
 * Validates a password
 * @param password Password to validate
 * @returns Whether the password is valid
 */
declare function isValidPassword(password: string): boolean;
/**
 * Validates a username
 * @param username Username to validate
 * @returns Whether the username is valid
 */
declare function isValidUsername(username: string): boolean;
/**
 * Validates a URL
 * @param url URL to validate
 * @returns Whether the URL is valid
 */
declare function isValidUrl(url: string): boolean;

export { AUTH_API, type Attachment, type AuthCredentials, CHAT_API, type ChatCompletionRequest, type ChatMessage, type ChatSession, type CreateTicketRequest, ErrorCode, ErrorMessage, type JwtPayload, OTRS_API, type OtrsTicket, type RefreshTokenRequest, TicketPriority, TicketStatus, type TokenResponse, type Tool, type ToolCall, type ToolCallResult, type UpdateTicketRequest, type User, WEBSOCKET_EVENTS, formatDate, formatFileSize, formatMessage, isValidEmail, isValidPassword, isValidUrl, isValidUsername, truncateString };
