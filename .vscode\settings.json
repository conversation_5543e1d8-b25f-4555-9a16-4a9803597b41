{"files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true, "**/out": true, "**/.turbo": true, "**/.cache": true, "**/.parcel-cache": true, "**/coverage": true, "**/.nyc_output": true, "**/__pycache__": true, "**/*.pyc": true, "**/.venv": true, "**/venv": true, "**/.ipynb_checkpoints": true, "**/*.log": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/*.tmp": true, "**/*.temp": true, "**/tmp": true, "**/temp": true, "**/*.tsbuildinfo": true, "**/.eslintcache": true, "**/.stylelintcache": true, "**/yarn-error.log": true, "**/npm-debug.log*": true, "**/.pnpm-debug.log*": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true, "**/out": true, "**/.turbo": true, "**/coverage": true, "**/.nyc_output": true, "**/__pycache__": true, "**/.venv": true, "**/venv": true, "**/.ipynb_checkpoints": true, "**/*.log": true, "**/yarn.lock": true, "**/package-lock.json": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/.next/**": true, "**/out/**": true, "**/.turbo/**": true, "**/coverage/**": true, "**/.nyc_output/**": true, "**/__pycache__/**": true, "**/.venv/**": true, "**/venv/**": true, "**/.ipynb_checkpoints/**": true, "**/*.log": true}, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "eslint.workingDirectories": ["packages/frontend", "packages/backend", "packages/mcp-server", "packages/rag-system", "packages/shared"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "emmet.includeLanguages": {"typescript": "typescriptreact", "javascript": "javascriptreact"}, "files.associations": {"*.env*": "dotenv"}, "json.schemas": [{"fileMatch": ["**/package.json"], "url": "https://json.schemastore.org/package.json"}, {"fileMatch": ["**/tsconfig*.json"], "url": "https://json.schemastore.org/tsconfig.json"}]}