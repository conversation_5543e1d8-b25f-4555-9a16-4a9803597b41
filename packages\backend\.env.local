# General
NODE_ENV=development
LOG_LEVEL=info

# Frontend
VITE_API_URL=http://localhost:4000
VITE_WEBSOCKET_URL=http://localhost:4000

# Backend
PORT=4000
CORS_ORIGIN=http://localhost:3000
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRATION=1h
JWT_REFRESH_EXPIRATION=7d

# LLM Configuration
LLM_PROVIDER=openai
LLM_API_KEY=sk-demo-key-replace-with-real-openai-key
LLM_MODEL=gpt-4o
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2000
LLM_TIMEOUT=30000

# MCP Server
MCP_SERVER_PORT=5000
MCP_SERVER_URL=http://localhost:5000

# OTRS API (Demo configuration - replace with real OTRS instance)
OTRS_API_URL=https://demo.otrs.com/api
OTRS_API_USERNAME=demo-user
OTRS_API_PASSWORD=demo-password

# RAG System
RAG_SYSTEM_PORT=6000
RAG_SYSTEM_URL=http://localhost:6000
EMBEDDING_API_KEY=sk-demo-embedding-key-replace-with-real-key
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSIONS=1536
VECTOR_DB_URL=http://localhost:7000
VECTOR_DB_API_KEY=demo-vector-db-key
