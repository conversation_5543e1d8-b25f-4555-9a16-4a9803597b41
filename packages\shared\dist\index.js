"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  AUTH_API: () => AUTH_API,
  CHAT_API: () => CHAT_API,
  ErrorCode: () => ErrorCode,
  ErrorMessage: () => ErrorMessage,
  OTRS_API: () => OTRS_API,
  TicketPriority: () => TicketPriority,
  TicketStatus: () => TicketStatus,
  WEBSOCKET_EVENTS: () => WEBSOCKET_EVENTS,
  formatDate: () => formatDate,
  formatFileSize: () => formatFileSize,
  formatMessage: () => formatMessage,
  isValidEmail: () => isValidEmail,
  isValidPassword: () => isValidPassword,
  isValidUrl: () => isValidUrl,
  isValidUsername: () => isValidUsername,
  truncateString: () => truncateString
});
module.exports = __toCommonJS(src_exports);

// src/types/otrs.types.ts
var TicketStatus = /* @__PURE__ */ ((TicketStatus2) => {
  TicketStatus2["NEW"] = "new";
  TicketStatus2["OPEN"] = "open";
  TicketStatus2["PENDING"] = "pending";
  TicketStatus2["RESOLVED"] = "resolved";
  TicketStatus2["CLOSED"] = "closed";
  return TicketStatus2;
})(TicketStatus || {});
var TicketPriority = /* @__PURE__ */ ((TicketPriority2) => {
  TicketPriority2["LOW"] = "low";
  TicketPriority2["MEDIUM"] = "medium";
  TicketPriority2["HIGH"] = "high";
  TicketPriority2["URGENT"] = "urgent";
  return TicketPriority2;
})(TicketPriority || {});

// src/constants/api.constants.ts
var CHAT_API = {
  SEND_MESSAGE: "/api/chat/messages",
  GET_HISTORY: "/api/chat/history",
  GET_SESSION: "/api/chat/sessions/:sessionId",
  CREATE_SESSION: "/api/chat/sessions",
  DELETE_SESSION: "/api/chat/sessions/:sessionId"
};
var AUTH_API = {
  LOGIN: "/api/auth/login",
  LOGOUT: "/api/auth/logout",
  REFRESH_TOKEN: "/api/auth/refresh-token",
  REGISTER: "/api/auth/register",
  ME: "/api/auth/me"
};
var OTRS_API = {
  CREATE_TICKET: "/api/otrs/tickets",
  UPDATE_TICKET: "/api/otrs/tickets/:ticketId",
  GET_TICKET: "/api/otrs/tickets/:ticketId",
  SEARCH_TICKETS: "/api/otrs/tickets/search",
  GET_QUEUES: "/api/otrs/queues",
  GET_AGENTS: "/api/otrs/agents"
};
var WEBSOCKET_EVENTS = {
  CONNECT: "connect",
  DISCONNECT: "disconnect",
  MESSAGE: "message",
  TYPING: "typing",
  ERROR: "error"
};

// src/constants/error.constants.ts
var ErrorCode = /* @__PURE__ */ ((ErrorCode2) => {
  ErrorCode2["INVALID_CREDENTIALS"] = "INVALID_CREDENTIALS";
  ErrorCode2["TOKEN_EXPIRED"] = "TOKEN_EXPIRED";
  ErrorCode2["UNAUTHORIZED"] = "UNAUTHORIZED";
  ErrorCode2["FORBIDDEN"] = "FORBIDDEN";
  ErrorCode2["MESSAGE_FAILED"] = "MESSAGE_FAILED";
  ErrorCode2["SESSION_NOT_FOUND"] = "SESSION_NOT_FOUND";
  ErrorCode2["TICKET_CREATION_FAILED"] = "TICKET_CREATION_FAILED";
  ErrorCode2["TICKET_UPDATE_FAILED"] = "TICKET_UPDATE_FAILED";
  ErrorCode2["TICKET_NOT_FOUND"] = "TICKET_NOT_FOUND";
  ErrorCode2["LLM_REQUEST_FAILED"] = "LLM_REQUEST_FAILED";
  ErrorCode2["TOOL_EXECUTION_FAILED"] = "TOOL_EXECUTION_FAILED";
  ErrorCode2["EMBEDDING_FAILED"] = "EMBEDDING_FAILED";
  ErrorCode2["RETRIEVAL_FAILED"] = "RETRIEVAL_FAILED";
  ErrorCode2["VALIDATION_ERROR"] = "VALIDATION_ERROR";
  ErrorCode2["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
  ErrorCode2["NOT_FOUND"] = "NOT_FOUND";
  ErrorCode2["BAD_REQUEST"] = "BAD_REQUEST";
  return ErrorCode2;
})(ErrorCode || {});
var ErrorMessage = {
  ["INVALID_CREDENTIALS" /* INVALID_CREDENTIALS */]: "Invalid username or password",
  ["TOKEN_EXPIRED" /* TOKEN_EXPIRED */]: "Authentication token has expired",
  ["UNAUTHORIZED" /* UNAUTHORIZED */]: "You are not authenticated",
  ["FORBIDDEN" /* FORBIDDEN */]: "You do not have permission to access this resource",
  ["MESSAGE_FAILED" /* MESSAGE_FAILED */]: "Failed to send message",
  ["SESSION_NOT_FOUND" /* SESSION_NOT_FOUND */]: "Chat session not found",
  ["TICKET_CREATION_FAILED" /* TICKET_CREATION_FAILED */]: "Failed to create OTRS ticket",
  ["TICKET_UPDATE_FAILED" /* TICKET_UPDATE_FAILED */]: "Failed to update OTRS ticket",
  ["TICKET_NOT_FOUND" /* TICKET_NOT_FOUND */]: "OTRS ticket not found",
  ["LLM_REQUEST_FAILED" /* LLM_REQUEST_FAILED */]: "Failed to communicate with LLM service",
  ["TOOL_EXECUTION_FAILED" /* TOOL_EXECUTION_FAILED */]: "Failed to execute tool",
  ["EMBEDDING_FAILED" /* EMBEDDING_FAILED */]: "Failed to generate embeddings",
  ["RETRIEVAL_FAILED" /* RETRIEVAL_FAILED */]: "Failed to retrieve relevant information",
  ["VALIDATION_ERROR" /* VALIDATION_ERROR */]: "Validation error",
  ["INTERNAL_SERVER_ERROR" /* INTERNAL_SERVER_ERROR */]: "Internal server error",
  ["NOT_FOUND" /* NOT_FOUND */]: "Resource not found",
  ["BAD_REQUEST" /* BAD_REQUEST */]: "Bad request"
};

// src/utils/formatting.ts
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString();
}
function truncateString(str, maxLength) {
  if (str.length <= maxLength) {
    return str;
  }
  return str.slice(0, maxLength) + "...";
}
function formatMessage(message) {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return message.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
}
function formatFileSize(bytes) {
  if (bytes === 0)
    return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// src/utils/validation.ts
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
function isValidPassword(password) {
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
  return passwordRegex.test(password);
}
function isValidUsername(username) {
  const usernameRegex = /^[a-zA-Z0-9]{3,20}$/;
  return usernameRegex.test(username);
}
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  AUTH_API,
  CHAT_API,
  ErrorCode,
  ErrorMessage,
  OTRS_API,
  TicketPriority,
  TicketStatus,
  WEBSOCKET_EVENTS,
  formatDate,
  formatFileSize,
  formatMessage,
  isValidEmail,
  isValidPassword,
  isValidUrl,
  isValidUsername,
  truncateString
});
