const express = require('express');
const cors = require('cors');

const app = express();
const port = process.env.MCP_SERVER_PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock OTRS ticket creation function
function createOtrsTicket(args) {
  const { title, description, priority = 'medium', queue = 'General', customer } = args;
  
  // Simulate ticket creation with mock data
  const ticketId = `TICKET-${Math.floor(Math.random() * 10000)}`;
  
  return {
    success: true,
    ticketId,
    message: `Ticket ${ticketId} created successfully`,
    ticket: {
      id: ticketId,
      title,
      description,
      priority,
      queue,
      customer,
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  };
}

// Mock OTRS ticket search function
function searchOtrsTickets(args) {
  const { query, limit = 10 } = args;
  
  // Simulate search results with mock data
  const tickets = [];
  const numResults = Math.min(limit, Math.floor(Math.random() * 5) + 1);
  
  for (let i = 0; i < numResults; i++) {
    tickets.push({
      id: `TICKET-${Math.floor(Math.random() * 10000)}`,
      title: `Sample ticket ${i + 1} matching "${query}"`,
      description: `This is a sample ticket description for query: ${query}`,
      priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
      status: ['new', 'open', 'pending', 'closed'][Math.floor(Math.random() * 4)],
      queue: 'General',
      customer: '<EMAIL>',
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }
  
  return {
    success: true,
    message: `Found ${tickets.length} tickets matching "${query}"`,
    tickets,
  };
}

// Tool registry
const tools = {
  'create-ticket': createOtrsTicket,
  'search-tickets': searchOtrsTickets,
};

// API endpoint to execute a tool
app.post('/api/execute-tool', async (req, res) => {
  try {
    const { toolName, arguments: args } = req.body;
    
    if (!toolName) {
      return res.status(400).json({ error: 'Tool name is required' });
    }
    
    const tool = tools[toolName];
    
    if (!tool) {
      return res.status(404).json({ error: `Tool "${toolName}" not found` });
    }
    
    console.log(`Executing tool: ${toolName}`, args);
    const result = tool(args);
    
    return res.json({ success: true, result });
  } catch (error) {
    console.error('Error executing tool:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while executing the tool';
    return res.status(500).json({ 
      error: errorMessage
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'MCP Server is running' });
});

// List available tools
app.get('/api/tools', (req, res) => {
  res.json({
    tools: Object.keys(tools).map(name => ({
      name,
      description: `Tool for ${name.replace('-', ' ')}`
    }))
  });
});

// Start the server
app.listen(port, () => {
  console.log(`MCP Server running on port ${port}`);
  console.log(`Available endpoints:`);
  console.log(`  - GET  /health`);
  console.log(`  - GET  /api/tools`);
  console.log(`  - POST /api/execute-tool`);
});

module.exports = app;
