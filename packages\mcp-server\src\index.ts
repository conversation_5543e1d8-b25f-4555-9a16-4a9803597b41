import express from 'express';
import dotenv from 'dotenv';
import { createTicket } from './tools/otrs/create-ticket';
import { updateTicket } from './tools/otrs/update-ticket';
import { searchTickets } from './tools/otrs/search-tickets';
import { logger } from './utils/logger';

// Load environment variables
dotenv.config();

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(express.json());

// Tool registry
const tools = {
  'create-ticket': createTicket,
  'update-ticket': updateTicket,
  'search-tickets': searchTickets,
  // Add more tools as needed
} as const;

type ToolName = keyof typeof tools;

// API endpoint to execute a tool
app.post('/api/execute-tool', async (req, res) => {
  try {
    const { toolName, arguments: args } = req.body;

    if (!toolName) {
      return res.status(400).json({ error: 'Tool name is required' });
    }

    const tool = tools[toolName as ToolName];

    if (!tool) {
      return res.status(404).json({ error: `Tool "${toolName}" not found` });
    }

    logger.info(`Executing tool: ${toolName}`);
    const result = await tool(args);

    return res.json({ success: true, result });
  } catch (error) {
    logger.error('Error executing tool:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while executing the tool';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Start the server
app.listen(port, () => {
  logger.info(`MCP Server running on port ${port}`);
});

export default app;
