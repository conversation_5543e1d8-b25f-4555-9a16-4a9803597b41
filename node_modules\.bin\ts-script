#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../../packages/backend/node_modules/ts-node/dist/bin-script-deprecated.js" "$@"
  ret=$?
else 
  node  "$basedir/../../packages/backend/node_modules/ts-node/dist/bin-script-deprecated.js" "$@"
  ret=$?
fi
exit $ret
